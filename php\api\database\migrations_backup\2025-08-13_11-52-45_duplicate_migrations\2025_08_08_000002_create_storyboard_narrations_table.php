<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建分镜旁白/解说表
 * 
 * 🚨 架构边界规范：
 * ✅ 旁白/解说文本由"Py视频创作工具"直接从AI平台获取
 * ✅ API服务器只负责管理旁白的URL、状态、元数据等附件信息
 * ✅ 严禁在API服务器上进行任何形式的旁白文件生成、处理、存储、中转下载
 * 
 * 🎬 分镜旁白功能：
 * ✅ 支持分镜旁白/解说文本管理
 * ✅ 支持多种旁白类型（背景音乐、音效、解说等）
 * ✅ 支持旁白时长和同步信息
 * ✅ 支持旁白生成参数和元数据
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('storyboard_narrations', function (Blueprint $table) {
            $table->id()->comment('旁白ID');
            $table->bigInteger('storyboard_id')->unsigned()->comment('分镜ID，关联p_project_storyboards表');
            $table->enum('narration_type', ['background_music', 'sound_effect', 'voice_over', 'ambient'])->comment('旁白类型');
            $table->string('title', 200)->comment('旁白标题');
            $table->text('content')->nullable()->comment('旁白内容/脚本');
            $table->decimal('start_time', 8, 3)->default(0)->comment('开始时间（秒）');
            $table->decimal('duration', 8, 3)->nullable()->comment('持续时间（秒）');
            $table->decimal('volume', 3, 2)->default(1.00)->comment('音量（0.00-1.00）');
            $table->enum('fade_in', ['none', 'linear', 'exponential'])->default('none')->comment('淡入效果');
            $table->enum('fade_out', ['none', 'linear', 'exponential'])->default('none')->comment('淡出效果');
            $table->text('ai_prompt')->nullable()->comment('AI生成提示词');
            $table->json('generation_params')->nullable()->comment('生成参数');
            $table->enum('status', ['draft', 'generating', 'completed', 'failed'])->default('draft')->comment('状态');
            $table->string('audio_url', 500)->nullable()->comment('音频文件URL（由Py工具从AI平台下载）');
            $table->bigInteger('resource_id')->unsigned()->nullable()->comment('关联资源ID，关联p_resources表');
            $table->json('sync_markers')->nullable()->comment('同步标记点');
            $table->json('metadata')->nullable()->comment('旁白元数据');
            $table->timestamps();
            
            // 索引
            $table->index('storyboard_id', 'idx_narration_storyboard');
            $table->index('narration_type', 'idx_narration_type');
            $table->index('status', 'idx_narration_status');
            $table->index(['storyboard_id', 'start_time'], 'idx_storyboard_timeline');
            $table->index('resource_id', 'idx_narration_resource');
            
            // 外键约束
            $table->foreign('storyboard_id')->references('id')->on('project_storyboards')->onDelete('cascade');
            $table->foreign('resource_id')->references('id')->on('resources')->onDelete('set null');
            
            $table->comment('分镜旁白/解说表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('storyboard_narrations');
    }
};
