<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建项目场景表
 * 存储从AI返回的分镜剧本中提取的场景信息
 * 
 * 🎬 功能说明：
 * ✅ 存储场景名称、空间、时间、天气等信息
 * ✅ 支持场景提示词用于AI生图
 * ✅ 建立与项目的外键关联
 * ✅ 支持场景顺序排序
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('project_scenarios', function (Blueprint $table) {
            $table->id()->comment('场景ID');
            $table->bigInteger('project_id')->unsigned()->comment('项目ID，关联p_projects表');
            $table->string('scene_name', 200)->comment('场景名称');
            $table->string('space', 50)->comment('空间（室内/室外）');
            $table->string('time', 100)->comment('时间信息');
            $table->string('weather', 100)->comment('天气信息');
            $table->text('scene_prompt')->nullable()->comment('场景提示词（AI生图的物理场景提示词）');
            $table->integer('scene_order')->comment('场景顺序');
            $table->json('metadata')->nullable()->comment('场景元数据');
            $table->timestamps();
            
            // 索引
            $table->index(['project_id', 'scene_order'], 'idx_project_order');
            $table->index('project_id', 'idx_project_id');
            
            // 外键约束
            $table->foreign('project_id')->references('id')->on('projects')->onDelete('cascade');
            
            // 唯一约束
            $table->unique(['project_id', 'scene_order'], 'unique_project_scene_order');
            
            $table->comment('项目场景表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('project_scenarios');
    }
};
