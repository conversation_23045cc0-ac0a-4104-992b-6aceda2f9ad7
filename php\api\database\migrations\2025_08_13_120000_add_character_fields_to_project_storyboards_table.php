<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 为项目分镜表添加角色字段
 * 
 * 🎬 功能说明：
 * ✅ 添加主要角色字段（primary_character_id）
 * ✅ 添加角色列表字段（character_ids）用于快速查询
 * ✅ 添加相关索引和外键约束
 * ✅ 保持与现有 storyboard_characters 关联表的兼容性
 * 
 * 🔗 设计理念：
 * - primary_character_id: 指向该分镜的主要角色，便于快速查询和显示
 * - character_ids: JSON格式存储所有相关角色ID，用于快速筛选和统计
 * - 与 storyboard_characters 表形成互补：主表存储快速查询字段，关联表存储详细信息
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('project_storyboards', function (Blueprint $table) {
            // 添加主要角色字段
            $table->bigInteger('primary_character_id')->unsigned()->nullable()
                  ->after('action_library_id')
                  ->comment('主要角色ID，关联project_characters表');
            
            // 添加角色列表字段（JSON格式，用于快速查询）
            $table->json('character_ids')->nullable()
                  ->after('primary_character_id')
                  ->comment('分镜中所有角色ID列表（JSON数组），用于快速筛选');
            
            // 添加索引
            $table->index('primary_character_id', 'idx_primary_character');
            
            // 添加外键约束
            $table->foreign('primary_character_id')
                  ->references('id')
                  ->on('project_characters')
                  ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('project_storyboards', function (Blueprint $table) {
            // 删除外键约束
            $table->dropForeign(['primary_character_id']);
            
            // 删除索引
            $table->dropIndex('idx_primary_character');
            
            // 删除字段
            $table->dropColumn(['primary_character_id', 'character_ids']);
        });
    }
};
