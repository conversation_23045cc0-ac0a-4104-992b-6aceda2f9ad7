<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建分镜角色关联表
 * 管理分镜与角色的关联关系
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('storyboard_characters', function (Blueprint $table) {
            $table->id()->comment('关联ID');
            $table->bigInteger('storyboard_id')->unsigned()->comment('分镜ID，关联p_project_storyboards表');
            $table->bigInteger('character_id')->unsigned()->nullable()->comment('角色ID，关联p_character_library表');
            $table->bigInteger('project_character_id')->unsigned()->nullable()->comment('项目角色ID，关联p_project_characters表');
            
            // 角色在分镜中的信息
            $table->string('character_name', 100)->comment('角色名称');
            $table->text('character_description')->nullable()->comment('角色在此分镜中的描述');
            $table->string('position_description', 200)->nullable()->comment('角色位置描述');
            $table->text('action_description')->nullable()->comment('角色动作描述');
            
            // 绑定状态
            $table->enum('binding_status', ['unbound', 'bound', 'generating'])
                  ->default('unbound')->comment('绑定状态');
            
            // 时间戳
            $table->timestamps();
            
            // 索引
            $table->index('storyboard_id', 'idx_storyboard');
            $table->index('character_id', 'idx_character');
            $table->index('project_character_id', 'idx_project_character');
            $table->index('binding_status', 'idx_binding_status');
            
            // 外键
            $table->foreign('storyboard_id')->references('id')->on('project_storyboards')->onDelete('cascade');
            $table->foreign('character_id')->references('id')->on('character_library')->onDelete('set null');
            $table->foreign('project_character_id')->references('id')->on('project_characters')->onDelete('set null');
            
            $table->comment('分镜角色关联表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('storyboard_characters');
    }
};
