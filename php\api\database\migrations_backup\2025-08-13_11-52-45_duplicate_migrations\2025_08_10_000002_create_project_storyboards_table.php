<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建项目分镜表（完整版）
 * 包含最新的字段结构：场景关联、字幕字段等
 * 
 * 🎬 功能说明：
 * ✅ 支持场景关联（scenarios_id外键）
 * ✅ 使用subtitle字段替代scene_description
 * ✅ 移除不需要的字段（camera_angle等6个字段）
 * ✅ 包含完整的索引和外键约束
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('project_storyboards', function (Blueprint $table) {
            $table->id()->comment('分镜ID');
            $table->bigInteger('project_id')->unsigned()->comment('项目ID，关联p_projects表');
            $table->bigInteger('scenarios_id')->unsigned()->comment('场景ID，关联p_project_scenarios表');
            $table->integer('scene_number')->comment('分镜序号');
            $table->string('scene_title', 200)->comment('分镜标题');
            $table->text('subtitle')->comment('分镜字幕（原scene_description字段）');
            
            // 技术参数
            $table->text('ai_prompt')->nullable()->comment('AI生成提示词');
            $table->json('generation_params')->nullable()->comment('生成参数');
            
            // 时间和状态
            $table->enum('status', ['draft', 'approved', 'generating', 'completed', 'failed'])
                  ->default('draft')->comment('分镜状态');
            
            // 资源关联
            $table->bigInteger('generated_image_id')->unsigned()->nullable()->comment('生成的图片资源ID，关联p_resources表');
            $table->bigInteger('generated_video_id')->unsigned()->nullable()->comment('生成的视频资源ID，关联p_resources表');
            $table->bigInteger('action_library_id')->unsigned()->nullable()->comment('分镜动作库ID，关联p_storyboard_action_library表');
            
            // 元数据
            $table->json('metadata')->nullable()->comment('分镜元数据');
            
            // 时间戳
            $table->timestamps();
            
            // 索引
            $table->index(['project_id', 'scene_number'], 'idx_project_scene');
            $table->index(['project_id', 'status'], 'idx_project_status');
            $table->index('scenarios_id', 'idx_scenarios_id');
            $table->index('status', 'idx_status');
            $table->index('action_library_id', 'idx_action_library_id');
            
            // 外键约束
            $table->foreign('project_id')->references('id')->on('projects')->onDelete('cascade');
            $table->foreign('scenarios_id')->references('id')->on('project_scenarios')->onDelete('cascade');
            $table->foreign('generated_image_id')->references('id')->on('resources')->onDelete('set null');
            $table->foreign('generated_video_id')->references('id')->on('resources')->onDelete('set null');
            $table->foreign('action_library_id')->references('id')->on('storyboard_action_library')->onDelete('set null');
            
            // 唯一约束
            $table->unique(['project_id', 'scene_number'], 'unique_project_scene');
            
            $table->comment('项目分镜表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('project_storyboards');
    }
};
