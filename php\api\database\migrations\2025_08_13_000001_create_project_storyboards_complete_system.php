<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建项目分镜完整系统
 * 整合所有与 project_storyboards 相关的表创建
 * 
 * 🎬 包含表结构：
 * ✅ project_scenarios - 项目场景表
 * ✅ storyboard_action_library - 分镜动作库表
 * ✅ project_storyboards - 项目分镜表（主表）
 * ✅ storyboard_characters - 分镜角色关联表
 * ✅ storyboard_narrations - 分镜旁白/解说表
 * 
 * 🔗 依赖关系：
 * 1. project_scenarios (依赖 projects)
 * 2. storyboard_action_library (依赖 users)
 * 3. project_storyboards (依赖 projects, project_scenarios, resources, storyboard_action_library)
 * 4. storyboard_characters (依赖 project_storyboards, character_library, project_characters)
 * 5. storyboard_narrations (依赖 project_storyboards, resources)
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // ==================== 1. 创建项目场景表 ====================
        Schema::create('project_scenarios', function (Blueprint $table) {
            $table->id()->comment('场景ID');
            $table->bigInteger('project_id')->unsigned()->comment('项目ID，关联p_projects表');
            $table->string('scene_name', 200)->comment('场景名称');
            $table->string('space', 50)->comment('空间（室内/室外）');
            $table->string('time', 100)->comment('时间信息');
            $table->string('weather', 100)->comment('天气信息');
            $table->text('scene_prompt')->nullable()->comment('场景提示词（AI生图的物理场景提示词）');
            $table->integer('scene_order')->comment('场景顺序');
            $table->json('metadata')->nullable()->comment('场景元数据');
            $table->timestamps();
            
            // 索引
            $table->index(['project_id', 'scene_order'], 'idx_project_order');
            $table->index('project_id', 'idx_project_id');
            
            // 外键约束
            $table->foreign('project_id')->references('id')->on('projects')->onDelete('cascade');
            
            // 唯一约束
            $table->unique(['project_id', 'scene_order'], 'unique_project_scene_order');
            
            $table->comment('项目场景表');
        });

        // ==================== 2. 创建分镜动作库表 ====================
        Schema::create('p_storyboard_action_library', function (Blueprint $table) {
            $table->id()->comment('动作库ID');
            
            // 基本信息
            $table->string('title', 255)->comment('动作标题');
            $table->text('description')->nullable()->comment('动作描述');
            
            // 镜头参数
            $table->enum('camera_shot', [
                'close_up',           // 特写
                'medium_shot',        // 中景
                'long_shot',          // 远景
                'extreme_close_up',   // 大特写
                'extreme_long_shot',  // 大远景
                'over_shoulder',      // 过肩镜头
                'bird_eye',           // 鸟瞰
                'worm_eye'            // 仰视
            ])->comment('镜头参数（特写/中景/远景/大特写/大远景/过肩镜头/鸟瞰/仰视）');
            
            // 动作信息
            $table->string('action_type', 100)->comment('动作类型（躺着/睡觉/向上看/开门/坐下/走路/跑步/跳跃/挥手/拥抱等）');
            $table->enum('difficulty_level', ['easy', 'medium', 'hard'])->default('medium')->comment('动作难度（简单/中等/困难）');

            // 资源文件
            $table->string('sketch_image_url', 500)->nullable()->comment('简笔画动作图URL');
            
            // 上传信息
            $table->boolean('is_user_uploaded')->default(false)->comment('是否用户上传（0=否，1=是）');
            $table->bigInteger('uploaded_by')->unsigned()->nullable()->comment('上传用户ID，关联users表');
            
            // 扩展信息
            $table->json('tags')->nullable()->comment('动作标签（JSON数组）');
            $table->integer('usage_count')->default(0)->comment('使用次数');
            $table->integer('sort_order')->default(0)->comment('排序权重');
            
            // 时间戳
            $table->timestamps();
            
            // 索引
            $table->index('camera_shot', 'idx_camera_shot');
            $table->index('action_type', 'idx_action_type');
            $table->index('difficulty_level', 'idx_difficulty_level');
            $table->index('is_user_uploaded', 'idx_is_user_uploaded');
            $table->index('uploaded_by', 'idx_uploaded_by');
            $table->index('usage_count', 'idx_usage_count');
            $table->index('sort_order', 'idx_sort_order');
            
            // 外键约束
            $table->foreign('uploaded_by')->references('id')->on('p_users')->onDelete('set null');
            
            $table->comment('分镜动作库表');
        });

        // ==================== 3. 创建项目分镜表（主表） ====================
        Schema::create('project_storyboards', function (Blueprint $table) {
            $table->id()->comment('分镜ID');
            $table->bigInteger('project_id')->unsigned()->comment('项目ID，关联projects表');
            $table->bigInteger('scenarios_id')->unsigned()->comment('场景ID，关联project_scenarios表');
            $table->integer('scene_number')->comment('分镜序号');
            $table->string('scene_title', 200)->comment('分镜标题');
            $table->text('subtitle')->comment('分镜字幕（原scene_description字段）');

            // 技术参数
            $table->text('ai_prompt')->nullable()->comment('AI生成提示词');
            $table->json('generation_params')->nullable()->comment('生成参数');

            // 时间和状态
            $table->enum('status', ['draft', 'approved', 'generating', 'completed', 'failed'])
                  ->default('draft')->comment('分镜状态');

            // 资源关联
            $table->bigInteger('generated_image_id')->unsigned()->nullable()->comment('生成的图片资源ID，关联resources表');
            $table->bigInteger('generated_video_id')->unsigned()->nullable()->comment('生成的视频资源ID，关联resources表');
            $table->bigInteger('action_library_id')->unsigned()->nullable()->comment('分镜动作库ID，关联storyboard_action_library表');

            // 元数据
            $table->json('metadata')->nullable()->comment('分镜元数据');

            // 时间戳
            $table->timestamps();

            // 索引
            $table->index(['project_id', 'scene_number'], 'idx_project_scene');
            $table->index(['project_id', 'status'], 'idx_project_status');
            $table->index('scenarios_id', 'idx_scenarios_id');
            $table->index('status', 'idx_status');
            $table->index('action_library_id', 'idx_action_library_id');

            // 外键约束
            $table->foreign('project_id')->references('id')->on('projects')->onDelete('cascade');
            $table->foreign('scenarios_id')->references('id')->on('project_scenarios')->onDelete('cascade');
            $table->foreign('generated_image_id')->references('id')->on('resources')->onDelete('set null');
            $table->foreign('generated_video_id')->references('id')->on('resources')->onDelete('set null');
            $table->foreign('action_library_id')->references('id')->on('p_storyboard_action_library')->onDelete('set null');

            // 唯一约束
            $table->unique(['project_id', 'scene_number'], 'unique_project_scene');

            $table->comment('项目分镜表');
        });

        // ==================== 4. 创建分镜角色关联表 ====================
        Schema::create('storyboard_characters', function (Blueprint $table) {
            $table->id()->comment('关联ID');
            $table->bigInteger('storyboard_id')->unsigned()->comment('分镜ID，关联project_storyboards表');
            $table->bigInteger('character_id')->unsigned()->nullable()->comment('角色ID，关联character_library表');
            $table->bigInteger('project_character_id')->unsigned()->nullable()->comment('项目角色ID，关联project_characters表');

            // 角色在分镜中的信息
            $table->string('character_name', 100)->comment('角色名称');
            $table->text('character_description')->nullable()->comment('角色在此分镜中的描述');
            $table->string('position_description', 200)->nullable()->comment('角色位置描述');
            $table->text('action_description')->nullable()->comment('角色动作描述');

            // 绑定状态
            $table->enum('binding_status', ['unbound', 'bound', 'generating'])
                  ->default('unbound')->comment('绑定状态');

            // 时间戳
            $table->timestamps();

            // 索引
            $table->index('storyboard_id', 'idx_storyboard');
            $table->index('character_id', 'idx_character');
            $table->index('project_character_id', 'idx_project_character');
            $table->index('binding_status', 'idx_binding_status');

            // 外键
            $table->foreign('storyboard_id')->references('id')->on('project_storyboards')->onDelete('cascade');
            $table->foreign('character_id')->references('id')->on('character_library')->onDelete('set null');
            $table->foreign('project_character_id')->references('id')->on('project_characters')->onDelete('set null');

            $table->comment('分镜角色关联表');
        });

        // ==================== 5. 创建分镜旁白/解说表 ====================
        Schema::create('storyboard_narrations', function (Blueprint $table) {
            $table->id()->comment('旁白ID');
            $table->bigInteger('storyboard_id')->unsigned()->comment('分镜ID，关联project_storyboards表');
            $table->enum('narration_type', ['background_music', 'sound_effect', 'voice_over', 'ambient'])->comment('旁白类型');
            $table->string('title', 200)->comment('旁白标题');
            $table->text('content')->nullable()->comment('旁白内容/脚本');
            $table->decimal('start_time', 8, 3)->default(0)->comment('开始时间（秒）');
            $table->decimal('duration', 8, 3)->nullable()->comment('持续时间（秒）');
            $table->decimal('volume', 3, 2)->default(1.00)->comment('音量（0.00-1.00）');
            $table->enum('fade_in', ['none', 'linear', 'exponential'])->default('none')->comment('淡入效果');
            $table->enum('fade_out', ['none', 'linear', 'exponential'])->default('none')->comment('淡出效果');
            $table->text('ai_prompt')->nullable()->comment('AI生成提示词');
            $table->json('generation_params')->nullable()->comment('生成参数');
            $table->enum('status', ['draft', 'generating', 'completed', 'failed'])->default('draft')->comment('状态');
            $table->string('audio_url', 500)->nullable()->comment('音频文件URL（由Py工具从AI平台下载）');
            $table->bigInteger('resource_id')->unsigned()->nullable()->comment('关联资源ID，关联resources表');
            $table->json('sync_markers')->nullable()->comment('同步标记点');
            $table->json('metadata')->nullable()->comment('旁白元数据');
            $table->timestamps();

            // 索引
            $table->index('storyboard_id', 'idx_narration_storyboard');
            $table->index('narration_type', 'idx_narration_type');
            $table->index('status', 'idx_narration_status');
            $table->index(['storyboard_id', 'start_time'], 'idx_storyboard_timeline');
            $table->index('resource_id', 'idx_narration_resource');

            // 外键约束
            $table->foreign('storyboard_id')->references('id')->on('project_storyboards')->onDelete('cascade');
            $table->foreign('resource_id')->references('id')->on('resources')->onDelete('set null');

            $table->comment('分镜旁白/解说表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 按照依赖关系的逆序删除表
        // 先删除依赖其他表的表，最后删除被依赖的表

        Schema::dropIfExists('storyboard_narrations');
        Schema::dropIfExists('storyboard_characters');
        Schema::dropIfExists('project_storyboards');
        Schema::dropIfExists('p_storyboard_action_library');
        Schema::dropIfExists('project_scenarios');
    }
};
